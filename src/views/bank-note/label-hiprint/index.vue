<template>
  <div class="label-hiprint-page">
    <!-- 页面头部 -->
    <el-card shadow="never" class="header-card">
      <template #header>
        <div class="header-content">
          <span class="title">标签模板设计器</span>
          <div class="header-actions">
            <el-button
              type="primary"
              :icon="Plus"
              @click="handleNewTemplate"
            >
              新建模板
            </el-button>
            <el-button
              :icon="Refresh"
              @click="loadTemplateList"
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 模板列表 -->
      <div class="template-list">
        <el-row :gutter="16">
          <el-col
            v-for="template in templateList"
            :key="template.id"
            :span="6"
          >
            <el-card
              :class="['template-card', { active: currentTemplate?.id === template.id }]"
              @click="selectTemplate(template)"
            >
              <template #header>
                <div class="template-header">
                  <span class="template-name">{{ template.templateName }}</span>
                  <el-dropdown trigger="click" @command="(cmd) => handleTemplateAction(cmd, template)">
                    <el-button type="text" :icon="ArrowDown" size="small" />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="edit">编辑</el-dropdown-item>
                        <el-dropdown-item command="preview">预览</el-dropdown-item>
                        <el-dropdown-item command="copy">复制</el-dropdown-item>
                        <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
              <div class="template-info">
                <p class="template-type">{{ template.templateType === 'CUSTOM' ? '自定义模板' : '系统模板' }}</p>
                <p class="template-desc">{{ template.description || '暂无描述' }}</p>
                <p class="template-time">{{ dayjs(template.createTime).format('YYYY-MM-DD HH:mm') }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 设计器区域 -->
    <el-card v-if="showDesigner" shadow="never" class="designer-card">
      <template #header>
        <div class="designer-header">
          <div class="designer-title">
            <span>{{ isEditMode ? '编辑模板' : '新建模板' }}: </span>
            <EditableTemplateName
              v-model="currentTemplateName"
              :placeholder="'未命名模板'"
              @change="handleTemplateNameChange"
            />
          </div>
          <div class="designer-actions">
            <el-button @click="handleSaveTemplate" type="primary" :loading="saving">
              保存模板
            </el-button>
            <el-button @click="handlePreview" type="success">
              预览效果
            </el-button>
            <el-button @click="testTemplateData" type="info" size="small">
              测试模板数据
            </el-button>
            <el-button @click="closeDesigner">
              关闭设计器
            </el-button>
          </div>
        </div>
      </template>

      <!-- 纸张设置工具栏 -->
      <PaperSettings
        :hiprint-template="hiprintTemplate"
        @paper-change="handlePaperChange"
        @orientation-change="handleOrientationChange"
      />

      <!-- hiprint 设计器容器 -->
      <div class="hiprint-container">
        <div class="hiprint-panel-left">
          <!-- 左侧面板：元素库 -->
          <ElementLibrary />
        </div>
        <div
          id="hiprint-panel-center"
          class="hiprint-panel-center"
        >
          <!-- 中间面板：设计区域 -->
        </div>
        <div class="hiprint-panel-right">
          <!-- 右侧面板：hiprint官方属性设置 -->
          <div id="PrintElementOptionSetting" class="hiprint-option-setting">
            <!-- hiprint会自动在这里渲染属性面板 -->
          </div>
        </div>
      </div>
    </el-card>

    <!-- 模板保存对话框 -->
    <el-dialog
      v-model="showSaveDialog"
      title="保存模板"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="saveFormRef"
        :model="saveForm"
        :rules="saveRules"
        label-width="100px"
      >
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="saveForm.templateName"
            placeholder="请输入模板名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="模板类型" prop="templateType">
          <el-select v-model="saveForm.templateType" placeholder="请选择模板类型">
            <el-option label="自定义模板" value="CUSTOM" />
            <el-option label="系统模板" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item label="设为默认">
          <el-switch v-model="saveForm.isDefault" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="saveForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSaveDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmSaveTemplate" :loading="saving">
          确定保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="模板预览"
      width="90%"
      :close-on-click-modal="false"
      class="preview-dialog"
    >
      <!-- 预览工具栏 -->
      <div class="preview-toolbar">
        <div class="toolbar-left">
          <el-button-group>
            <el-button
              :type="previewMode === 'design' ? 'primary' : ''"
              size="small"
              @click="setPreviewMode('design')"
            >
              设计预览
            </el-button>
            <el-button
              :type="previewMode === 'data' ? 'primary' : ''"
              size="small"
              @click="setPreviewMode('data')"
            >
              数据预览
            </el-button>
          </el-button-group>

          <el-divider direction="vertical" />

          <span class="toolbar-label">缩放：</span>
          <el-select v-model="previewScale" size="small" style="width: 100px" @change="updatePreviewScale">
            <el-option label="50%" value="0.5" />
            <el-option label="75%" value="0.75" />
            <el-option label="100%" value="1" />
            <el-option label="125%" value="1.25" />
            <el-option label="150%" value="1.5" />
            <el-option label="200%" value="2" />
          </el-select>

          <el-divider direction="vertical" />

          <span class="toolbar-label">纸张：</span>
          <el-select v-model="previewPaper" size="small" style="width: 120px" @change="updatePreviewPaper">
            <el-option label="A4" value="A4" />
            <el-option label="A5" value="A5" />
            <el-option label="标签纸" value="LABEL" />
            <el-option label="自定义" value="CUSTOM" />
          </el-select>
        </div>

        <div class="toolbar-right">
          <el-button-group>
            <el-button size="small" @click="printPreview">
              打印
            </el-button>
            <el-button size="small" @click="exportPreview">
              导出
            </el-button>
            <el-button size="small" @click="refreshPreview">
              刷新
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 预览内容区域 -->
      <div class="preview-content">
        <div class="preview-wrapper" :style="previewWrapperStyle">
          <div id="preview-container" class="preview-container" :class="previewPaper.toLowerCase()">
            <div v-if="previewLoading" class="preview-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>正在生成预览...</span>
            </div>
            <div v-else-if="previewError" class="preview-error">
              <el-icon><WarningFilled /></el-icon>
              <span>{{ previewError }}</span>
              <el-button type="primary" size="small" @click="refreshPreview">重试</el-button>
            </div>
            <!-- 预览内容将在这里渲染 -->
          </div>
        </div>
      </div>

      <!-- 数据预览模式的数据选择 -->
      <div v-if="previewMode === 'data'" class="data-preview-panel">
        <el-divider>数据填充</el-divider>
        <el-form :model="previewData" label-width="120px" size="small">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="钱币名称：">
                <el-input v-model="previewData.coinName" placeholder="请输入钱币名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="面值：">
                <el-input v-model="previewData.faceValue" placeholder="请输入面值" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="年份：">
                <el-input v-model="previewData.year" placeholder="请输入年份" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="评级：">
                <el-input v-model="previewData.grade" placeholder="请输入评级" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="二维码：">
                <el-input v-model="previewData.qrcode" placeholder="请输入二维码内容" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="条形码：">
                <el-input v-model="previewData.barcode" placeholder="请输入条形码内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="applyPreviewData">应用数据</el-button>
            <el-button @click="resetPreviewData">重置数据</el-button>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="closePreviewDialog">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { Plus, Refresh, ArrowDown, Loading, WarningFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import dayjs from 'dayjs';

// 组件导入
import ElementLibrary from './components/ElementLibrary.vue';
import PaperSettings from './components/PaperSettings.vue';
import EditableTemplateName from './components/EditableTemplateName.vue';

// API 导入
import {
  getTemplateList,
  saveTemplate,
  deleteTemplate as deleteTemplateApi,
  getAvailableFields,
  getFieldsByCategory,
  previewLabel
} from './api';

// hiprint 相关导入
import {
  initHiprint as initHiprintPlugin,
  createPrintTemplate,
  designerConfig,
  handleHiprintError,
  getFieldElementTemplate,
  safeAddElement,
  detectHiprintAPI,
  buildDraggableElements,
  setPaperSize,
  setElementSelectCallback,
  setElementChangeCallback,
  updateElementProperty,
  copyElement,
  deleteElement,
  getHiprintInstance,
  cleanupTemplate
} from '@/utils/hiprint-config';

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const templateList = ref([]);
const currentTemplate = ref(null);
const showDesigner = ref(false);
const isEditMode = ref(false);
const showSaveDialog = ref(false);
const showPreviewDialog = ref(false);

// 组件挂载状态管理
const isComponentMounted = ref(false);

// 预览相关状态
const previewMode = ref('design'); // 'design' | 'data'
const previewScale = ref('1');
const previewPaper = ref('A4');
const previewLoading = ref(false);
const previewError = ref('');

// 预览数据
const previewData = reactive({
  coinName: '示例钱币',
  faceValue: '10元',
  year: '2023',
  grade: 'MS70',
  qrcode: 'https://example.com/qr',
  barcode: '1234567890'
});

// hiprint 实例
let hiprintTemplate = null;
let availableFields = [];

// 当前模板名称
const currentTemplateName = ref('');

// 保存表单
const saveFormRef = ref();
const saveForm = reactive({
  templateName: '',
  templateType: 'CUSTOM',
  isDefault: false,
  description: ''
});

const saveRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  templateType: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ]
};

// 安全的样式绑定计算属性
const previewWrapperStyle = computed(() => {
  if (!previewScale.value || !isComponentMounted.value) return {};

  try {
    const scale = parseFloat(previewScale.value) || 1;
    return {
      transform: `scale(${scale})`,
      transformOrigin: 'center top',
      transition: 'transform 0.3s ease'
    };
  } catch (error) {
    console.warn('预览缩放样式计算失败:', error);
    return {
      transform: 'scale(1)',
      transformOrigin: 'center top'
    };
  }
});

// 监听对话框状态变化
watch(showPreviewDialog, (newVal) => {
  if (!newVal) {
    // 对话框关闭时清理状态
    previewLoading.value = false;
    previewError.value = '';
  }
});

// 方法定义
const loadTemplateList = async () => {
  loading.value = true;
  try {
    const data = await getTemplateList();
    templateList.value = data || [];
  } catch (error) {
    EleMessage.error('加载模板列表失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

const loadAvailableFields = async () => {
  try {
    availableFields = await getFieldsByCategory();
  } catch (error) {
    console.error('加载可用字段失败:', error);
  }
};

const selectTemplate = (template) => {
  currentTemplate.value = template;
};

const handleTemplateAction = async (command, template) => {
  switch (command) {
    case 'edit':
      editTemplate(template);
      break;
    case 'preview':
      await previewTemplate(template);
      break;
    case 'copy':
      await copyTemplate(template);
      break;
    case 'delete':
      await deleteTemplate(template);
      break;
  }
};

const handleNewTemplate = () => {
  currentTemplate.value = null;
  currentTemplateName.value = '';
  isEditMode.value = false;
  showDesigner.value = true;
  nextTick(() => {
    initDesigner();
  });
};

const editTemplate = (template) => {
  currentTemplate.value = template;
  currentTemplateName.value = template.templateName || '';
  isEditMode.value = true;
  showDesigner.value = true;
  nextTick(() => {
    initDesigner(template);
  });
};

const closeDesigner = () => {
  showDesigner.value = false;
  if (hiprintTemplate) {
    cleanupTemplate(hiprintTemplate);
    hiprintTemplate = null;
  }
};

const handleTemplateNameChange = (newName) => {
  currentTemplateName.value = newName;
};

const handleSaveTemplate = () => {
  if (!hiprintTemplate) {
    EleMessage.error('请先设计模板内容');
    return;
  }

  // 获取模板数据
  const templateData = hiprintTemplate.getJson();
  if (!templateData || !templateData.panels || templateData.panels.length === 0) {
    EleMessage.warning('模板内容为空，请先添加一些元素');
    return;
  }

  // 设置保存表单数据
  saveForm.templateName = currentTemplateName.value || '未命名模板';
  saveForm.templateType = 'CUSTOM';
  saveForm.isDefault = false;
  saveForm.description = '';

  showSaveDialog.value = true;
};

const confirmSaveTemplate = async () => {
  if (!saveFormRef.value) return;

  try {
    await saveFormRef.value.validate();

    saving.value = true;

    const templateData = hiprintTemplate.getJson();
    const templateInfo = {
      id: currentTemplate.value?.id,
      templateName: saveForm.templateName,
      templateType: saveForm.templateType,
      isDefault: saveForm.isDefault,
      description: saveForm.description,
      layoutConfig: JSON.stringify(templateData)
    };

    await saveTemplate(templateInfo);

    EleMessage.success('模板保存成功');
    showSaveDialog.value = false;

    // 刷新模板列表
    await loadTemplateList();

  } catch (error) {
    if (error.message) {
      EleMessage.error('保存失败：' + error.message);
    }
  } finally {
    saving.value = false;
  }
};

const copyTemplate = async (template) => {
  try {
    const newTemplate = {
      ...template,
      id: undefined,
      templateName: template.templateName + '_副本',
      isDefault: false
    };

    await saveTemplate(newTemplate);
    EleMessage.success('模板复制成功');
    await loadTemplateList();
  } catch (error) {
    EleMessage.error('复制失败：' + error.message);
  }
};

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.templateName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await deleteTemplateApi(template.id);
    EleMessage.success('删除成功');
    await loadTemplateList();

    // 如果删除的是当前选中的模板，清空选中状态
    if (currentTemplate.value?.id === template.id) {
      currentTemplate.value = null;
    }
  } catch (error) {
    if (error !== 'cancel') {
      EleMessage.error('删除失败：' + error.message);
    }
  }
};

// 预览相关方法
const setPreviewMode = (mode) => {
  previewMode.value = mode;
  refreshPreview();
};

const updatePreviewScale = () => {
  // 缩放变化时不需要重新渲染，样式会自动更新
};

const updatePreviewPaper = () => {
  // 纸张变化时可能需要重新渲染
  refreshPreview();
};

const printPreview = () => {
  const previewContainer = document.getElementById('preview-container');
  if (previewContainer) {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>打印预览</title>
          <style>
            body { margin: 0; padding: 20px; }
            .preview-container { background: white; }
          </style>
        </head>
        <body>
          ${previewContainer.innerHTML}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  }
};

const exportPreview = () => {
  const previewContainer = document.getElementById('preview-container');
  if (previewContainer) {
    const exportWindow = window.open('', '_blank');
    exportWindow.document.write(`
      <html>
        <head>
          <title>导出预览</title>
          <style>
            body { margin: 0; padding: 20px; }
            .preview-container { background: white; }
          </style>
        </head>
        <body>
          ${previewContainer.innerHTML}
        </body>
      </html>
    `);
    exportWindow.document.close();
  }
};

const refreshPreview = async () => {
  if (!showPreviewDialog.value || !isComponentMounted.value) {
    return;
  }

  previewLoading.value = true;
  previewError.value = '';

  try {
    await nextTick();

    const previewContainer = document.getElementById('preview-container');
    if (!previewContainer) {
      throw new Error('预览容器不存在');
    }

    // 安全清理容器内容
    try {
      previewContainer.innerHTML = '';
    } catch (e) {
      console.warn('清理预览容器失败:', e);
    }

    // 再次检查容器是否存在（防止异步过程中被销毁）
    if (!document.getElementById('preview-container')) {
      console.warn('预览容器在渲染过程中被销毁');
      return;
    }

    if (hiprintTemplate) {
      // 安全的DOM操作
      if (previewContainer && previewContainer.parentNode) {
        if (typeof hiprintTemplate.getHtml === 'function') {
          const htmlContent = hiprintTemplate.getHtml();
          previewContainer.innerHTML = htmlContent;
        } else if (typeof hiprintTemplate.print === 'function') {
          hiprintTemplate.print(previewContainer, {
            preview: true
          });
        } else {
          throw new Error('模板不支持预览功能');
        }
      }
    }
  } catch (error) {
    console.error('预览渲染失败:', error);
    if (showPreviewDialog.value) {
      previewError.value = error.message || '预览渲染失败';
    }
  } finally {
    previewLoading.value = false;
  }
};

const handlePreview = () => {
  if (!hiprintTemplate) {
    EleMessage.error('请先设计模板内容');
    return;
  }

  try {
    // 获取模板数据验证是否有内容
    const templateData = hiprintTemplate.getJson();
    if (!templateData || !templateData.panels || templateData.panels.length === 0) {
      EleMessage.warning('模板内容为空，请先添加一些元素');
      return;
    }

    showPreviewDialog.value = true;
    nextTick(() => {
      refreshPreview();
    });
  } catch (error) {
    console.error('预览失败:', error);
    EleMessage.error('预览失败: ' + error.message);
  }
};

const previewTemplate = async (template) => {
  try {
    // 确保 hiprint 已初始化
    await initHiprintPlugin();

    showPreviewDialog.value = true;
    nextTick(async () => {
      if (!showPreviewDialog.value || !isComponentMounted.value) {
        return;
      }

      const previewContainer = document.getElementById('preview-container');
      if (previewContainer && template.layoutConfig) {
        previewContainer.innerHTML = '';
        try {
          const templateData = JSON.parse(template.layoutConfig);
          // 使用修复后的 createPrintTemplate 函数
          const tempTemplate = createPrintTemplate(templateData, {
            settingContainer: null // 预览时不需要属性面板
          });

          if (previewContainer && previewContainer.parentNode) {
            tempTemplate.print(previewContainer, {
              preview: true
            });
          }
        } catch (parseError) {
          console.error('解析模板数据失败:', parseError);
          EleMessage.error('模板数据格式错误，无法预览');
        }
      }
    });
  } catch (error) {
    const errorMessage = handleHiprintError(error);
    EleMessage.error('预览失败：' + errorMessage);
  }
};

const closePreviewDialog = () => {
  showPreviewDialog.value = false;

  // 清理预览状态
  previewLoading.value = false;
  previewError.value = '';

  // 清理预览容器
  nextTick(() => {
    const previewContainer = document.getElementById('preview-container');
    if (previewContainer) {
      try {
        previewContainer.innerHTML = '';
      } catch (e) {
        console.warn('清理预览容器失败:', e);
      }
    }
  });
};

const applyPreviewData = () => {
  // 应用预览数据到模板
  refreshPreview();
};

const resetPreviewData = () => {
  // 重置预览数据
  Object.assign(previewData, {
    coinName: '示例钱币',
    faceValue: '10元',
    year: '2023',
    grade: 'MS70',
    qrcode: 'https://example.com/qr',
    barcode: '1234567890'
  });
  refreshPreview();
};

const initDesigner = async (template = null) => {
  try {
    console.log('开始初始化设计器...', template ? '编辑模式' : '新建模式');

    // 初始化 hiprint 插件
    await initHiprintPlugin();

    // 清理之前的实例
    if (hiprintTemplate) {
      cleanupTemplate(hiprintTemplate);
      hiprintTemplate = null;
    }

    // 设置元素选中和变更回调
    setElementSelectCallback((element) => {
      console.log('元素被选中:', element);
    });

    setElementChangeCallback((element, property, value) => {
      console.log('元素属性变更:', element, property, value);
    });

    // 创建模板实例
    const templateOptions = {
      settingContainer: '#PrintElementOptionSetting',
      paginationContainer: '.hiprint-printPagination',
      dataMode: 1,
      history: true,
      onImageChooseClick: (target) => {
        console.log('图片选择被点击:', target);
      }
    };

    if (template && template.layoutConfig) {
      console.log('加载现有模板数据...');
      try {
        const templateData = JSON.parse(template.layoutConfig);
        console.log('解析的模板数据:', templateData);

        if (templateData && (templateData.panels || templateData.template)) {
          console.log('模板数据结构有效，开始创建模板...');
          hiprintTemplate = createPrintTemplate(templateData, templateOptions);
          console.log('模板创建成功，面板数量:', hiprintTemplate.panels?.length || 0);

          await nextTick();

          if (hiprintTemplate && typeof hiprintTemplate.design === 'function') {
            console.log('开始渲染设计器...');
            hiprintTemplate.design('#hiprint-panel-center');
            console.log('设计器渲染完成');
          } else {
            console.error('模板实例没有design方法');
          }
        } else {
          console.warn('模板数据结构无效，创建空模板');
          hiprintTemplate = createPrintTemplate(null, templateOptions);
          await nextTick();
          if (hiprintTemplate && typeof hiprintTemplate.design === 'function') {
            hiprintTemplate.design('#hiprint-panel-center');
          }
        }
      } catch (parseError) {
        console.error('解析模板数据失败:', parseError);
        EleMessage.error('模板数据格式错误，创建空模板');
        hiprintTemplate = createPrintTemplate(null, templateOptions);
        await nextTick();
        if (hiprintTemplate && typeof hiprintTemplate.design === 'function') {
          hiprintTemplate.design('#hiprint-panel-center');
        }
      }
    } else {
      console.log('创建新模板...');
      hiprintTemplate = createPrintTemplate(null, templateOptions);
      await nextTick();
      if (hiprintTemplate && typeof hiprintTemplate.design === 'function') {
        hiprintTemplate.design('#hiprint-panel-center');
      }
    }

    // 确保模板有面板
    if (!hiprintTemplate.panels || hiprintTemplate.panels.length === 0) {
      console.log('模板没有面板，创建默认面板...');
      if (typeof hiprintTemplate.addPrintPanel === 'function') {
        const panel = hiprintTemplate.addPrintPanel();
        console.log('创建了新面板:', panel);
      } else {
        console.warn('模板没有addPrintPanel方法');
      }
    }

    // 构建可拖拽元素
    setTimeout(async () => {
      try {
        console.log('开始构建拖拽元素...');
        const result = await buildDraggableElements('.ep-draggable-item');
        console.log('拖拽元素构建结果:', result);
      } catch (buildError) {
        console.error('构建拖拽元素失败:', buildError);
      }
    }, 1000);

  } catch (error) {
    const errorMessage = handleHiprintError(error);
    console.error('初始化设计器失败:', error);
    EleMessage.error('初始化设计器失败：' + errorMessage);
  }
};

// 处理纸张变化
const handlePaperChange = (paperInfo) => {
  console.log('纸张已更改:', paperInfo);
};

// 处理纸张方向变化
const handleOrientationChange = (orientation) => {
  console.log('纸张方向已更改:', orientation);
};

// 测试模板数据回显
const testTemplateData = () => {
  if (!hiprintTemplate) {
    EleMessage.error('模板未初始化');
    return;
  }

  console.log('当前模板数据:', hiprintTemplate.getJson());
  console.log('模板面板数量:', hiprintTemplate.panels?.length || 0);

  if (hiprintTemplate.panels && hiprintTemplate.panels.length > 0) {
    hiprintTemplate.panels.forEach((panel, index) => {
      console.log(`面板 ${index}:`, {
        printElements: panel.printElements?.length || 0,
        width: panel.width,
        height: panel.height
      });
    });
  }
};

// 生命周期
onMounted(async () => {
  isComponentMounted.value = true;
  await loadTemplateList();
  await loadAvailableFields();
});

onUnmounted(() => {
  isComponentMounted.value = false;
  if (hiprintTemplate) {
    cleanupTemplate(hiprintTemplate);
    hiprintTemplate = null;
  }
});
</script>

<style scoped>
.label-hiprint-page {
  padding: 16px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.header-card {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.template-list {
  margin-top: 16px;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-name {
  font-weight: 600;
  color: #303133;
}

.template-info {
  padding: 8px 0;
}

.template-type {
  font-size: 12px;
  color: #909399;
  margin: 4px 0;
}

.template-desc {
  font-size: 12px;
  color: #606266;
  margin: 4px 0;
  line-height: 1.4;
}

.template-time {
  font-size: 11px;
  color: #C0C4CC;
  margin: 4px 0;
}

.designer-card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.designer-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.hiprint-container {
  display: flex;
  height: calc(100vh - 300px);
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.hiprint-panel-left {
  width: 280px;
  border-right: 1px solid #ddd;
  background: #f5f5f5;
  overflow-y: auto;
}

.hiprint-panel-center {
  flex: 1;
  background: #fff;
  position: relative;
}

.hiprint-panel-right {
  width: 320px;
  border-left: 1px solid #ddd;
  background: #f5f5f5;
  overflow-y: auto;
}

.hiprint-option-setting {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

#PrintElementOptionSetting {
  height: 100%;
  overflow-y: auto;
}

#PrintElementOptionSetting .hiprint-option-item {
  margin-bottom: 12px;
}

#PrintElementOptionSetting .hiprint-option-item-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  display: block;
}

#PrintElementOptionSetting .hiprint-option-item-content {
  width: 100%;
}

.preview-dialog {
  --el-dialog-content-font-size: 14px;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 16px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-label {
  font-size: 12px;
  color: #606266;
  margin-right: 4px;
}

.preview-content {
  min-height: 400px;
  max-height: 600px;
  overflow: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f5f5f5;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.preview-wrapper {
  transform-origin: center top;
  transition: transform 0.3s ease;
}

.preview-container {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  position: relative;
}

.preview-container.a4 {
  width: 210mm;
  min-height: 297mm;
}

.preview-container.a5 {
  width: 148mm;
  min-height: 210mm;
}

.preview-container.label {
  width: 100mm;
  min-height: 60mm;
}

.preview-container.custom {
  width: 200mm;
  min-height: 100mm;
}

.preview-loading,
.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.preview-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.preview-error .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #f56c6c;
}

.data-preview-panel {
  margin-top: 16px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 4px;
}
</style>
